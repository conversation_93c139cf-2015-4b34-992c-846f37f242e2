import os
import re
import PyPDF2
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.pipeline import Pipeline
import nltk
from datetime import datetime

try:
    from docx import Document as DocxDocument
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("Warning: python-docx not available. DOCX processing will be limited.")

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

class DocumentProcessor:
    def __init__(self):
        self.classifier = None
        self.categories = ['Academic', 'Business', 'Technical', 'Legal', 'Medical', 'General']
        self._initialize_classifier()
    
    def extract_text_from_pdf(self, file_path):
        """Extract text content from PDF file"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text.strip()
        except Exception as e:
            print(f"Error extracting text from PDF: {e}")
            return ""
    
    def extract_text_from_docx(self, file_path):
        """Extract text content from DOCX file"""
        try:
            if DOCX_AVAILABLE:
                doc = DocxDocument(file_path)
                text = ""
                for paragraph in doc.paragraphs:
                    text += paragraph.text + "\n"
                return text.strip()
            else:
                # Fallback when python-docx is not available
                return f"DOCX file: {os.path.basename(file_path)}"
        except Exception as e:
            print(f"Error extracting text from DOCX: {e}")
            return f"DOCX file: {os.path.basename(file_path)}"
    
    def extract_title_from_pdf(self, file_path):
        """Extract title from PDF metadata or content"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Try to get title from metadata
                if pdf_reader.metadata and pdf_reader.metadata.title:
                    return pdf_reader.metadata.title.strip()
                
                # Try to extract title from first page content
                if len(pdf_reader.pages) > 0:
                    first_page_text = pdf_reader.pages[0].extract_text()
                    lines = first_page_text.split('\n')
                    for line in lines[:10]:  # Check first 10 lines
                        line = line.strip()
                        if len(line) > 10 and len(line) < 200:  # Reasonable title length
                            return line
                
                # Fallback to filename
                return os.path.splitext(os.path.basename(file_path))[0]
        except Exception as e:
            print(f"Error extracting title from PDF: {e}")
            return os.path.splitext(os.path.basename(file_path))[0]
    
    def extract_title_from_docx(self, file_path):
        """Extract title from DOCX file"""
        try:
            if DOCX_AVAILABLE:
                doc = DocxDocument(file_path)

                # Try to get title from document properties
                if hasattr(doc.core_properties, 'title') and doc.core_properties.title:
                    return doc.core_properties.title.strip()

                # Try to extract title from first paragraph
                if doc.paragraphs:
                    for paragraph in doc.paragraphs[:5]:  # Check first 5 paragraphs
                        text = paragraph.text.strip()
                        if len(text) > 10 and len(text) < 200:  # Reasonable title length
                            return text

            # Fallback to filename
            return os.path.splitext(os.path.basename(file_path))[0]
        except Exception as e:
            print(f"Error extracting title from DOCX: {e}")
            return os.path.splitext(os.path.basename(file_path))[0]
    
    def extract_metadata_from_pdf(self, file_path):
        """Extract metadata from PDF file"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                metadata = {}
                
                if pdf_reader.metadata:
                    metadata['author'] = pdf_reader.metadata.author if pdf_reader.metadata.author else None
                    metadata['creation_date'] = pdf_reader.metadata.creation_date if pdf_reader.metadata.creation_date else None
                    metadata['last_modified'] = pdf_reader.metadata.modification_date if pdf_reader.metadata.modification_date else None
                
                return metadata
        except Exception as e:
            print(f"Error extracting metadata from PDF: {e}")
            return {}
    
    def extract_metadata_from_docx(self, file_path):
        """Extract metadata from DOCX file - simplified version"""
        try:
            # Return basic metadata for DOCX files
            stat = os.stat(file_path)
            metadata = {
                'author': None,
                'creation_date': datetime.fromtimestamp(getattr(stat, 'st_birthtime', stat.st_ctime)),
                'last_modified': datetime.fromtimestamp(stat.st_mtime)
            }
            return metadata
        except Exception as e:
            print(f"Error extracting metadata from DOCX: {e}")
            return {}
    
    def _initialize_classifier(self):
        """Initialize the document classifier with sample training data"""
        # Sample training data for different categories
        training_data = [
            ("research methodology analysis statistical significant", "Academic"),
            ("university college student education learning", "Academic"),
            ("business strategy market revenue profit", "Business"),
            ("company management financial report quarterly", "Business"),
            ("algorithm software programming code development", "Technical"),
            ("system architecture database network security", "Technical"),
            ("contract agreement legal terms conditions", "Legal"),
            ("court case law regulation compliance", "Legal"),
            ("medical patient treatment diagnosis therapy", "Medical"),
            ("health clinical study pharmaceutical drug", "Medical"),
            ("general information document text content", "General"),
            ("various topics discussion overview summary", "General")
        ]
        
        texts = [item[0] for item in training_data]
        labels = [item[1] for item in training_data]
        
        # Create and train the classifier
        self.classifier = Pipeline([
            ('tfidf', TfidfVectorizer(stop_words='english', max_features=1000)),
            ('classifier', MultinomialNB())
        ])
        
        self.classifier.fit(texts, labels)
    
    def classify_document(self, text):
        """Classify document based on its content"""
        if not text or not self.classifier:
            return "General", 0.5
        
        try:
            # Clean and preprocess text
            cleaned_text = re.sub(r'[^a-zA-Z\s]', '', text.lower())
            
            # Predict category and confidence
            prediction = self.classifier.predict([cleaned_text])[0]
            probabilities = self.classifier.predict_proba([cleaned_text])[0]
            confidence = max(probabilities)
            
            return prediction, confidence
        except Exception as e:
            print(f"Error classifying document: {e}")
            return "General", 0.5
    
    def highlight_text(self, text, keywords):
        """Highlight keywords in text"""
        if not keywords or not text:
            return text
        
        # Create a pattern that matches any of the keywords (case-insensitive)
        pattern = '|'.join(re.escape(keyword) for keyword in keywords)
        
        def replace_func(match):
            return f"<mark>{match.group()}</mark>"
        
        highlighted_text = re.sub(pattern, replace_func, text, flags=re.IGNORECASE)
        return highlighted_text
    
    def search_documents(self, documents, keywords):
        """Search documents for keywords and return matching documents"""
        if not keywords:
            return documents
        
        matching_docs = []
        keyword_list = [kw.strip().lower() for kw in keywords.split() if kw.strip()]
        
        for doc in documents:
            content = (doc.get('content_text', '') + ' ' + doc.get('title', '')).lower()
            
            # Check if any keyword is found in the document
            if any(keyword in content for keyword in keyword_list):
                # Highlight the keywords in the content
                highlighted_content = self.highlight_text(
                    doc.get('content_text', ''), 
                    keyword_list
                )
                doc_copy = doc.copy()
                doc_copy['highlighted_content'] = highlighted_content
                matching_docs.append(doc_copy)
        
        return matching_docs

