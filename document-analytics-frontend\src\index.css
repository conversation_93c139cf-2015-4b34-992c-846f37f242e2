@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for search highlighting */
mark {
  background-color: #fef08a; /* yellow-200 */
  color: #854d0e; /* yellow-800 */
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
}

/* Alternative highlighting styles */
.highlight-exact {
  background-color: #dcfce7; /* green-100 */
  color: #166534; /* green-800 */
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
}

.highlight-word {
  background-color: #fef3c7; /* amber-100 */
  color: #92400e; /* amber-800 */
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
}

/* Ensure proper text rendering */
.search-content {
  line-height: 1.5;
  word-wrap: break-word;
}

/* Scrollbar styling for search results */
.search-results::-webkit-scrollbar {
  width: 6px;
}

.search-results::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.search-results::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}