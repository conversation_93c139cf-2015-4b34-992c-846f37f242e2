#!/usr/bin/env python3
"""
مثال سريع لاختبار البحث المحسن
يمكن تشغيله بعد تشغيل الخادم لاختبار البحث بالجمل والعبارات
"""

import requests
import json

BASE_URL = "http://localhost:5000/api"

def test_search(query, description):
    """اختبار البحث مع وصف"""
    print(f"\n{'='*50}")
    print(f"اختبار: {description}")
    print(f"البحث عن: '{query}'")
    print(f"{'='*50}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/search",
            headers={"Content-Type": "application/json"},
            json={"keywords": query}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"عدد النتائج: {data['results_count']}")
            print(f"وقت البحث: {data['search_time']:.4f} ثانية")
            
            for i, doc in enumerate(data['documents'], 1):
                print(f"\nنتيجة {i}:")
                print(f"  العنوان: {doc['title']}")
                print(f"  نوع المطابقة: {doc.get('match_type', 'غير محدد')}")
                print(f"  المصطلحات المطابقة: {doc.get('matched_terms', [])}")
                
                # عرض جزء من المحتوى المميز
                highlighted = doc.get('highlighted_content', '')
                if highlighted:
                    preview = highlighted[:200] + "..." if len(highlighted) > 200 else highlighted
                    print(f"  معاينة المحتوى: {preview}")
        else:
            print(f"خطأ: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"خطأ في الاتصال: {e}")

def main():
    """تشغيل اختبارات البحث"""
    print("بدء اختبار البحث المحسن...")
    
    # إعادة تعيين قاعدة البيانات أولاً
    try:
        reset_response = requests.post(f"{BASE_URL}/debug/reset-db")
        if reset_response.status_code == 200:
            print("✅ تم إعادة تعيين قاعدة البيانات بنجاح")
        else:
            print("⚠️ فشل في إعادة تعيين قاعدة البيانات")
    except:
        print("⚠️ تأكد من تشغيل الخادم أولاً")
        return
    
    # اختبارات البحث
    test_cases = [
        ("business", "البحث عن كلمة واحدة"),
        ("market analysis", "البحث عن عبارة من كلمتين"),
        ("programming best practices", "البحث عن عبارة من ثلاث كلمات"),
        ("machine learning algorithms", "البحث عن عبارة تقنية"),
        ("artificial intelligence applications", "البحث عن عبارة طويلة"),
        ("quality assurance", "البحث عن مصطلح مهني"),
        ("statistical analysis", "البحث عن مصطلح أكاديمي"),
        ("revenue growth", "البحث عن مصطلح تجاري"),
        ("system architecture", "البحث عن مصطلح تقني"),
        ("غير موجود", "البحث عن شيء غير موجود")
    ]
    
    for query, description in test_cases:
        test_search(query, description)
    
    print(f"\n{'='*50}")
    print("انتهى الاختبار!")
    print("ملاحظة: تأكد من تشغيل الخادم على المنفذ 5000")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
