#!/usr/bin/env python3
"""
Quick test for highlighting functionality
"""

import requests
import json

BASE_URL = "http://localhost:5000/api"

def test_highlight_direct():
    """Test highlighting directly"""
    print("Testing text highlighting...")
    
    test_cases = [
        {
            "text": "This is a comprehensive business report about market analysis and trading strategies.",
            "terms": ["business"],
            "description": "Single word"
        },
        {
            "text": "This is a comprehensive business report about market analysis and trading strategies.",
            "terms": ["market analysis"],
            "description": "Two words phrase"
        },
        {
            "text": "This is a comprehensive business report about market analysis and trading strategies.",
            "terms": ["comprehensive business report"],
            "description": "Three words phrase"
        },
        {
            "text": "This is a comprehensive business report about market analysis and trading strategies.",
            "terms": ["business", "market"],
            "description": "Multiple individual words"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i}: {test_case['description']} ---")
        
        try:
            response = requests.post(f"{BASE_URL}/debug/test-highlight", 
                                   json={
                                       "text": test_case["text"],
                                       "terms": test_case["terms"]
                                   },
                                   headers={"Content-Type": "application/json"})
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Request successful")
                print(f"   Search terms: {data.get('search_terms', [])}")
                print(f"   Contains <mark> tags: {data.get('contains_mark_tags', False)}")
                print(f"   Original length: {data.get('text_length', 0)}")
                print(f"   Highlighted length: {data.get('highlighted_length', 0)}")
                
                highlighted = data.get('highlighted_text', '')
                if '<mark>' in highlighted:
                    print(f"   ✅ Highlighting working!")
                    # Show a snippet with highlighting
                    snippet = highlighted[:200] + '...' if len(highlighted) > 200 else highlighted
                    print(f"   Preview: {snippet}")
                else:
                    print(f"   ❌ No highlighting found")
                    print(f"   Text: {highlighted[:100]}...")
            else:
                print(f"❌ Request failed: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_search_with_highlight():
    """Test search endpoint to see if highlighting works"""
    print("\n" + "="*50)
    print("Testing search with highlighting...")
    
    test_queries = ["business", "market analysis", "comprehensive business report"]
    
    for query in test_queries:
        print(f"\n--- Testing search: '{query}' ---")
        
        try:
            response = requests.post(f"{BASE_URL}/search", 
                                   json={"keywords": query},
                                   headers={"Content-Type": "application/json"})
            
            if response.status_code == 200:
                data = response.json()
                documents = data.get('documents', [])
                print(f"✅ Found {len(documents)} documents")
                
                for i, doc in enumerate(documents[:2]):  # Check first 2 documents
                    print(f"   Document {i+1}: {doc.get('title', 'No title')}")
                    highlighted_content = doc.get('highlighted_content', '')
                    
                    if highlighted_content and '<mark>' in highlighted_content:
                        print(f"     ✅ Has highlighting")
                        # Count mark tags
                        mark_count = highlighted_content.count('<mark>')
                        print(f"     Found {mark_count} highlighted terms")
                    else:
                        print(f"     ❌ No highlighting found")
                        print(f"     Content preview: {highlighted_content[:100]}...")
                        
            else:
                print(f"❌ Search failed: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Run highlighting tests"""
    print("🔍 Quick Highlighting Test")
    print("=" * 50)
    
    # Test connection
    try:
        response = requests.get(f"{BASE_URL}/documents")
        if response.status_code != 200:
            print("❌ Cannot connect to Flask backend")
            return
    except Exception as e:
        print(f"❌ Cannot connect to Flask backend: {e}")
        return
    
    print("✅ Connected to Flask backend")
    
    # Run tests
    test_highlight_direct()
    test_search_with_highlight()
    
    print("\n" + "=" * 50)
    print("🎯 Highlighting test completed!")

if __name__ == "__main__":
    main()
