import os
import time
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from src.models.user import db
from src.models.document import Document, SearchLog
from src.utils.document_processor import DocumentProcessor

document_bp = Blueprint('document', __name__)
processor = DocumentProcessor()

UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'pdf', 'docx'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def ensure_upload_folder():
    upload_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), UPLOAD_FOLDER)
    if not os.path.exists(upload_path):
        os.makedirs(upload_path)
    return upload_path

@document_bp.route('/upload', methods=['POST'])
def upload_document():
    """Upload and process a document"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'File type not allowed. Only PDF and DOCX files are supported.'}), 400
        
        # Save the file
        upload_path = ensure_upload_folder()
        filename = secure_filename(file.filename)
        file_path = os.path.join(upload_path, filename)
        file.save(file_path)
        
        # Process the document
        file_extension = filename.rsplit('.', 1)[1].lower()
        
        if file_extension == 'pdf':
            title = processor.extract_title_from_pdf(file_path)
            content_text = processor.extract_text_from_pdf(file_path)
            metadata = processor.extract_metadata_from_pdf(file_path)
        else:  # docx
            title = processor.extract_title_from_docx(file_path)
            content_text = processor.extract_text_from_docx(file_path)
            metadata = processor.extract_metadata_from_docx(file_path)
        
        # Classify the document
        classification, confidence = processor.classify_document(content_text)
        
        # Get file size
        file_size = os.path.getsize(file_path)
        
        # Create document record
        document = Document(
            title=title,
            filename=filename,
            file_path=file_path,
            file_size=file_size,
            content_text=content_text,
            classification=classification,
            classification_confidence=confidence,
            author=metadata.get('author'),
            creation_date=metadata.get('creation_date'),
            last_modified=metadata.get('last_modified')
        )
        
        db.session.add(document)
        db.session.commit()
        
        return jsonify({
            'message': 'Document uploaded and processed successfully',
            'document': document.to_dict()
        }), 201
        
    except Exception as e:
        return jsonify({'error': f'Error processing document: {str(e)}'}), 500

@document_bp.route('/documents', methods=['GET'])
def get_documents():
    """Get all documents with optional sorting"""
    try:
        sort_by = request.args.get('sort_by', 'upload_date')
        sort_order = request.args.get('sort_order', 'desc')
        
        # Measure sorting time
        start_time = time.time()
        
        query = Document.query
        
        if sort_by == 'title':
            if sort_order == 'asc':
                query = query.order_by(Document.title.asc())
            else:
                query = query.order_by(Document.title.desc())
        elif sort_by == 'upload_date':
            if sort_order == 'asc':
                query = query.order_by(Document.upload_date.asc())
            else:
                query = query.order_by(Document.upload_date.desc())
        elif sort_by == 'file_size':
            if sort_order == 'asc':
                query = query.order_by(Document.file_size.asc())
            else:
                query = query.order_by(Document.file_size.desc())
        
        documents = query.all()
        sort_time = time.time() - start_time
        
        return jsonify({
            'documents': [doc.to_dict() for doc in documents],
            'sort_time': sort_time,
            'total_count': len(documents)
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Error retrieving documents: {str(e)}'}), 500

@document_bp.route('/search', methods=['POST'])
def search_documents():
    """Search documents by keywords"""
    try:
        data = request.get_json()
        keywords = data.get('keywords', '').strip()
        
        if not keywords:
            return jsonify({'error': 'Keywords are required'}), 400
        
        # Measure search time
        start_time = time.time()
        
        # Get all documents
        all_documents = Document.query.all()
        documents_data = [doc.to_dict() for doc in all_documents]
        
        # Search and highlight
        matching_documents = processor.search_documents(documents_data, keywords)
        
        search_time = time.time() - start_time
        
        # Log the search
        search_log = SearchLog(
            query=keywords,
            results_count=len(matching_documents),
            search_time=search_time
        )
        db.session.add(search_log)
        db.session.commit()
        
        return jsonify({
            'documents': matching_documents,
            'search_time': search_time,
            'results_count': len(matching_documents),
            'query': keywords
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Error searching documents: {str(e)}'}), 500

@document_bp.route('/classify', methods=['POST'])
def classify_documents():
    """Classify all documents or reclassify existing ones"""
    try:
        start_time = time.time()
        
        documents = Document.query.all()
        classified_count = 0
        
        for document in documents:
            if document.content_text:
                classification, confidence = processor.classify_document(document.content_text)
                document.classification = classification
                document.classification_confidence = confidence
                classified_count += 1
        
        db.session.commit()
        classification_time = time.time() - start_time
        
        return jsonify({
            'message': f'Successfully classified {classified_count} documents',
            'classification_time': classification_time,
            'classified_count': classified_count
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Error classifying documents: {str(e)}'}), 500

@document_bp.route('/statistics', methods=['GET'])
def get_statistics():
    """Get system statistics"""
    try:
        # Document statistics
        total_documents = Document.query.count()
        total_size = db.session.query(db.func.sum(Document.file_size)).scalar() or 0
        
        # Classification statistics
        classification_stats = db.session.query(
            Document.classification,
            db.func.count(Document.id)
        ).group_by(Document.classification).all()
        
        # Recent search statistics
        recent_searches = SearchLog.query.order_by(SearchLog.timestamp.desc()).limit(10).all()
        avg_search_time = db.session.query(db.func.avg(SearchLog.search_time)).scalar() or 0
        
        return jsonify({
            'total_documents': total_documents,
            'total_size_bytes': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'classification_distribution': {
                category: count for category, count in classification_stats
            },
            'recent_searches': [search.to_dict() for search in recent_searches],
            'average_search_time': round(avg_search_time, 4)
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Error retrieving statistics: {str(e)}'}), 500

@document_bp.route('/document/<int:document_id>', methods=['GET'])
def get_document(document_id):
    """Get a specific document by ID"""
    try:
        document = Document.query.get_or_404(document_id)
        return jsonify({'document': document.to_dict()}), 200
        
    except Exception as e:
        return jsonify({'error': f'Error retrieving document: {str(e)}'}), 500

@document_bp.route('/document/<int:document_id>', methods=['DELETE'])
def delete_document(document_id):
    """Delete a specific document"""
    try:
        document = Document.query.get_or_404(document_id)
        
        # Delete the file from filesystem
        if os.path.exists(document.file_path):
            os.remove(document.file_path)
        
        # Delete from database
        db.session.delete(document)
        db.session.commit()
        
        return jsonify({'message': 'Document deleted successfully'}), 200
        
    except Exception as e:
        return jsonify({'error': f'Error deleting document: {str(e)}'}), 500

