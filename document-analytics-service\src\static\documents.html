<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Analytics Service</title>
    <style>
        body { font-family: sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 30px; }
        .nav { display: flex; justify-content: center; margin-bottom: 30px; }
        .nav button { padding: 10px 20px; margin: 0 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 5px; }
        .nav button.active { background: #0056b3; }
        .section { margin-bottom: 20px; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center; }
        .stat-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; margin-top: 5px; }
        h1, h2 { margin-top: 0; color: #333; }
        label { display: inline-block; width: 120px; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 300px; padding: 8px; margin-bottom: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 10px 15px; margin-right: 10px; cursor: pointer; background: #28a745; color: white; border: none; border-radius: 4px; }
        button:hover { background: #218838; }
        pre { background-color: #f4f4f4; padding: 15px; border: 1px solid #ddd; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word; max-height: 400px; overflow-y: auto; }
        .hidden { display: none; }
        .search-results { margin-top: 20px; }
        .document-item { padding: 15px; margin: 10px 0; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }
        .document-title { font-weight: bold; color: #007bff; margin-bottom: 5px; }
        .document-meta { color: #666; font-size: 0.9em; margin-bottom: 10px; }
        .document-content { line-height: 1.5; }
        mark { background-color: #ffeb3b; padding: 2px 4px; border-radius: 2px; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .success { color: #155724; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Document Analytics Service</h1>
            <p>Upload, search, sort, and classify your documents with AI-powered analytics</p>
            <p id="api-info" style="font-size: 0.8em; color: #666;"></p>
        </div>

        <div class="nav">
            <button onclick="showSection('upload')" class="nav-btn">Upload</button>
            <button onclick="showSection('search')" class="nav-btn">Search</button>
            <button onclick="showSection('documents')" class="nav-btn">Documents</button>
            <button onclick="showSection('analytics')" class="nav-btn active">Analytics</button>
        </div>

        <!-- Analytics Section -->
        <div id="analytics-section" class="section">
            <h2>📊 Analytics</h2>
            <button onclick="testConnection()">Test API Connection</button>
            <button onclick="loadStatistics()">Refresh Statistics</button>
            <button onclick="resetDatabase()">Reset Database (Test Data)</button>
            
            <div id="stats-container">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-label">Total Documents</div>
                        <div class="stat-value" id="total-documents">0</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">Total Storage</div>
                        <div class="stat-value" id="total-storage">0 MB</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">Avg Search Time</div>
                        <div class="stat-value" id="avg-search-time">0s</div>
                    </div>
                </div>
                
                <h3>Document Classification Distribution</h3>
                <div id="classification-chart"></div>
                
                <h3>Recent Searches</h3>
                <div id="recent-searches"></div>
            </div>
        </div>

        <!-- Search Section -->
        <div id="search-section" class="section hidden">
            <h2>🔍 Search Documents</h2>
            <p>Search through your documents using keywords</p>
            
            <label for="search-keywords">Search Keywords:</label>
            <input type="text" id="search-keywords" placeholder="Enter keywords, phrases, or sentences..." />
            <button onclick="searchDocuments()">Search</button>
            
            <div id="search-results"></div>
        </div>

        <!-- Documents Section -->
        <div id="documents-section" class="section hidden">
            <h2>📄 Documents</h2>
            <button onclick="loadDocuments()">Load Documents</button>
            <button onclick="classifyDocuments()">Classify All Documents</button>
            
            <div id="documents-list"></div>
        </div>

        <!-- Upload Section -->
        <div id="upload-section" class="section hidden">
            <h2>📤 Upload Document</h2>
            <p>Upload PDF or DOCX files for analysis</p>
            
            <input type="file" id="file-input" accept=".pdf,.docx" />
            <button onclick="uploadDocument()">Upload</button>
            
            <div id="upload-result"></div>
        </div>
    </div>

    <script>
        // Auto-detect the correct API base URL
        const API_BASE_URL = `${window.location.protocol}//${window.location.hostname}:${window.location.port}/api`;

        // Show/Hide sections
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.add('hidden');
            });
            
            // Remove active class from all nav buttons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionName + '-section').classList.remove('hidden');
            
            // Add active class to clicked button
            event.target.classList.add('active');
            
            // Load data for specific sections
            if (sectionName === 'analytics') {
                loadStatistics();
            } else if (sectionName === 'documents') {
                loadDocuments();
            }
        }

        // Helper functions
        function displayResult(elementId, data) {
            const element = document.getElementById(elementId);
            if (typeof data === 'object') {
                element.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } else {
                element.innerHTML = data;
            }
        }

        function displayError(elementId, error) {
            document.getElementById(elementId).innerHTML = `<div class="error">Error: ${error}</div>`;
        }

        function displaySuccess(elementId, message) {
            document.getElementById(elementId).innerHTML = `<div class="success">${message}</div>`;
        }

        // Test API Connection
        async function testConnection() {
            try {
                console.log('Testing connection to:', `${API_BASE_URL}/health`);
                const response = await fetch(`${API_BASE_URL}/health`);
                console.log('Health check response status:', response.status);

                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const data = await response.json();
                console.log('Health check data:', data);

                displaySuccess('stats-container', `✅ API Connection Successful! Server is running and responding. Available endpoints: ${data.endpoints.join(', ')}`);

            } catch (error) {
                console.error('Connection test failed:', error);
                displayError('stats-container', `❌ API Connection Failed: ${error.message}. Make sure the server is running on the correct port.`);
            }
        }

        // Load Statistics
        async function loadStatistics() {
            try {
                console.log('Loading statistics from:', `${API_BASE_URL}/statistics`);
                const response = await fetch(`${API_BASE_URL}/statistics`);
                console.log('Statistics response status:', response.status);
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const data = await response.json();
                console.log('Statistics data received:', data);
                
                // Update stat cards
                document.getElementById('total-documents').textContent = data.total_documents || 0;
                document.getElementById('total-storage').textContent = `${data.total_size_mb || 0} MB`;
                document.getElementById('avg-search-time').textContent = `${data.average_search_time || 0}s`;
                
                // Display classification distribution
                const classificationDiv = document.getElementById('classification-chart');
                if (data.classification_distribution && Object.keys(data.classification_distribution).length > 0) {
                    let chartHtml = '<div style="display: flex; gap: 10px; flex-wrap: wrap;">';
                    for (const [category, count] of Object.entries(data.classification_distribution)) {
                        chartHtml += `<div class="stat-card" style="min-width: 150px;">
                            <div class="stat-value">${count}</div>
                            <div class="stat-label">${category}</div>
                        </div>`;
                    }
                    chartHtml += '</div>';
                    classificationDiv.innerHTML = chartHtml;
                } else {
                    classificationDiv.innerHTML = '<p>No classification data available</p>';
                }
                
                // Display recent searches
                const searchesDiv = document.getElementById('recent-searches');
                if (data.recent_searches && data.recent_searches.length > 0) {
                    let searchHtml = '<ul>';
                    data.recent_searches.forEach(search => {
                        searchHtml += `<li><strong>${search.query}</strong> - ${search.results_count} results (${search.search_time}s)</li>`;
                    });
                    searchHtml += '</ul>';
                    searchesDiv.innerHTML = searchHtml;
                } else {
                    searchesDiv.innerHTML = '<p>No recent searches</p>';
                }
                
            } catch (error) {
                console.error('Error loading statistics:', error);
                displayError('stats-container', `Failed to load statistics: ${error.message}`);
            }
        }

        // Reset Database
        async function resetDatabase() {
            try {
                console.log('Resetting database at:', `${API_BASE_URL}/debug/reset-db`);
                const response = await fetch(`${API_BASE_URL}/debug/reset-db`, { method: 'POST' });
                console.log('Reset response status:', response.status);
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const data = await response.json();
                console.log('Reset response data:', data);
                
                displaySuccess('stats-container', `Database reset successfully! Created ${data.sample_documents_created} documents and ${data.sample_searches_created} search logs.`);
                
                // Reload statistics
                setTimeout(loadStatistics, 1000);
                
            } catch (error) {
                console.error('Error resetting database:', error);
                displayError('stats-container', `Failed to reset database: ${error.message}`);
            }
        }

        // Search Documents
        async function searchDocuments() {
            const keywords = document.getElementById('search-keywords').value.trim();
            if (!keywords) {
                displayError('search-results', 'Please enter search keywords');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/search`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ keywords })
                });
                
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const data = await response.json();
                
                let resultsHtml = `<h3>Search Results (${data.results_count} found in ${data.search_time.toFixed(4)}s)</h3>`;
                
                if (data.documents && data.documents.length > 0) {
                    data.documents.forEach(doc => {
                        resultsHtml += `
                            <div class="document-item">
                                <div class="document-title">${doc.highlighted_title || doc.title}</div>
                                <div class="document-meta">
                                    Classification: ${doc.classification || 'None'} | 
                                    Size: ${(doc.file_size / 1024).toFixed(1)} KB |
                                    Match Type: ${doc.match_type || 'N/A'}
                                </div>
                                <div class="document-content">${doc.highlighted_content || doc.content_text || 'No content'}</div>
                            </div>
                        `;
                    });
                } else {
                    resultsHtml += '<p>No documents found matching your search criteria.</p>';
                }
                
                document.getElementById('search-results').innerHTML = resultsHtml;
                
            } catch (error) {
                console.error('Error searching documents:', error);
                displayError('search-results', `Search failed: ${error.message}`);
            }
        }

        // Load Documents
        async function loadDocuments() {
            try {
                const response = await fetch(`${API_BASE_URL}/documents`);
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const data = await response.json();
                
                let documentsHtml = `<h3>All Documents (${data.total_count})</h3>`;
                
                if (data.documents && data.documents.length > 0) {
                    data.documents.forEach(doc => {
                        documentsHtml += `
                            <div class="document-item">
                                <div class="document-title">${doc.title}</div>
                                <div class="document-meta">
                                    Classification: ${doc.classification || 'None'} | 
                                    Size: ${(doc.file_size / 1024).toFixed(1)} KB |
                                    Uploaded: ${new Date(doc.upload_date).toLocaleDateString()}
                                </div>
                                <div class="document-content">${doc.content_text ? doc.content_text.substring(0, 200) + '...' : 'No content'}</div>
                            </div>
                        `;
                    });
                } else {
                    documentsHtml += '<p>No documents found. Upload some documents first.</p>';
                }
                
                document.getElementById('documents-list').innerHTML = documentsHtml;
                
            } catch (error) {
                console.error('Error loading documents:', error);
                displayError('documents-list', `Failed to load documents: ${error.message}`);
            }
        }

        // Upload Document
        async function uploadDocument() {
            const fileInput = document.getElementById('file-input');
            const file = fileInput.files[0];
            
            if (!file) {
                displayError('upload-result', 'Please select a file to upload');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                const response = await fetch(`${API_BASE_URL}/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const data = await response.json();
                
                displaySuccess('upload-result', `Document uploaded successfully! Title: ${data.document.title}, Classification: ${data.document.classification}`);
                
                // Clear file input
                fileInput.value = '';
                
            } catch (error) {
                console.error('Error uploading document:', error);
                displayError('upload-result', `Upload failed: ${error.message}`);
            }
        }

        // Classify Documents
        async function classifyDocuments() {
            try {
                const response = await fetch(`${API_BASE_URL}/classify`, { method: 'POST' });
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const data = await response.json();
                
                displaySuccess('documents-list', `Classification completed! ${data.classified_count} documents classified in ${data.classification_time.toFixed(2)}s`);
                
                // Reload documents
                setTimeout(loadDocuments, 1000);
                
            } catch (error) {
                console.error('Error classifying documents:', error);
                displayError('documents-list', `Classification failed: ${error.message}`);
            }
        }

        // Load statistics on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Display API info
            document.getElementById('api-info').textContent = `API Base URL: ${API_BASE_URL}`;
            loadStatistics();
        });
    </script>
</body>
</html>
