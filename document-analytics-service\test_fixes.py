#!/usr/bin/env python3
"""
Test script to verify the fixes for document analytics service
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:5000/api"

def test_statistics():
    """Test statistics endpoint"""
    print("Testing statistics endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/statistics")
        if response.status_code == 200:
            data = response.json()
            print("✅ Statistics endpoint working")
            print(f"   Total documents: {data.get('total_documents', 0)}")
            print(f"   Total size: {data.get('total_size_mb', 0)} MB")
            print(f"   Classifications: {data.get('classification_distribution', {})}")
            return True
        else:
            print(f"❌ Statistics endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Statistics endpoint error: {e}")
        return False

def test_search(query):
    """Test search endpoint with a specific query"""
    print(f"\nTesting search with query: '{query}'...")
    try:
        response = requests.post(f"{BASE_URL}/search", 
                               json={"keywords": query},
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            data = response.json()
            results_count = len(data.get('documents', []))
            print(f"✅ Search working - Found {results_count} results")
            
            # Show details of first result if any
            if results_count > 0:
                first_doc = data['documents'][0]
                print(f"   First result: {first_doc.get('title', 'No title')}")
                print(f"   Match type: {first_doc.get('match_type', 'unknown')}")
                print(f"   Matched terms: {first_doc.get('matched_terms', [])}")
                
                # Check content length
                content = first_doc.get('content_text', '')
                print(f"   Content length: {len(content)} characters")
                if len(content) > 0:
                    print(f"   Content preview: {content[:200]}...")
                else:
                    print("   ⚠️  No content found in document")
            
            return True
        else:
            print(f"❌ Search failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Search error: {e}")
        return False

def test_debug_search(query):
    """Test debug search endpoint"""
    print(f"\nTesting debug search with query: '{query}'...")
    try:
        response = requests.post(f"{BASE_URL}/debug/test-search", 
                               json={"keywords": query},
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            data = response.json()
            debug_info = data.get('debug_info', {})
            print(f"✅ Debug search working")
            print(f"   Total documents in DB: {debug_info.get('total_documents_in_db', 0)}")
            print(f"   Search results: {data.get('results_count', 0)}")
            
            # Show document content previews
            previews = debug_info.get('documents_content_preview', [])
            for i, preview in enumerate(previews):
                print(f"   Document {i+1}: {preview.get('title', 'No title')}")
                print(f"     Content length: {preview.get('content_length', 0)}")
                print(f"     Contains keyword: {preview.get('content_contains_keyword', False)}")
                if preview.get('content_length', 0) > 0:
                    content_preview = preview.get('content_preview', '')[:100]
                    print(f"     Preview: {content_preview}...")
            
            return True
        else:
            print(f"❌ Debug search failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Debug search error: {e}")
        return False

def test_reprocess_documents():
    """Test document reprocessing"""
    print("\nTesting document reprocessing...")
    try:
        response = requests.post(f"{BASE_URL}/debug/reprocess-documents")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Document reprocessing completed")
            print(f"   Processed: {data.get('processed_count', 0)} documents")
            print(f"   Total documents: {data.get('total_documents', 0)}")
            
            errors = data.get('errors', [])
            if errors:
                print(f"   ⚠️  Errors: {len(errors)}")
                for error in errors[:3]:  # Show first 3 errors
                    print(f"     - {error}")
            
            return True
        else:
            print(f"❌ Document reprocessing failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Document reprocessing error: {e}")
        return False

def main():
    """Run all tests"""
    print("🔍 Testing Document Analytics Service Fixes")
    print("=" * 50)
    
    # Test basic connectivity
    try:
        response = requests.get(f"{BASE_URL}/documents")
        if response.status_code != 200:
            print("❌ Cannot connect to Flask backend. Make sure it's running on port 5000")
            return
    except Exception as e:
        print(f"❌ Cannot connect to Flask backend: {e}")
        print("Make sure Flask is running on http://localhost:5000")
        return
    
    print("✅ Connected to Flask backend")
    
    # Run tests
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Statistics
    total_tests += 1
    if test_statistics():
        tests_passed += 1
    
    # Test 2: Search with single word
    total_tests += 1
    if test_search("business"):
        tests_passed += 1
    
    # Test 3: Search with multiple words
    total_tests += 1
    if test_search("business report"):
        tests_passed += 1
    
    # Test 4: Search with full sentence
    total_tests += 1
    if test_search("comprehensive business report about market analysis"):
        tests_passed += 1
    
    # Test 5: Debug search
    total_tests += 1
    if test_debug_search("technical documentation"):
        tests_passed += 1
    
    # Test 6: Reprocess documents (if needed)
    total_tests += 1
    if test_reprocess_documents():
        tests_passed += 1
    
    # Final results
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The fixes are working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
